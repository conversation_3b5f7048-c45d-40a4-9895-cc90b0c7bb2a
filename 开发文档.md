项目名称：
liteniche 

开发环境：
Nginx 1.26.1+PHP 8.3.21+MySQL 8.0.24

技术栈：
- 后端：PHP 8.3.21
- 前端样式：Tailwind CSS
- 前端交互：原生 JavaScript
- 数据库：MySQL 8.0.24
- Web服务器：Nginx 1.26.1

编辑器：
ckeditor（免费版本ckeditor_4.25.1-lts_standard）

数据库信息：
数据库名：liteniche_com
用户名：liteniche_usr
密码：Rs8aJxESnHBSERNE

项目目录结构：

```
liteniche/                          # Web服务器根目录
├── index.php                       # 前台首页
├── .htaccess                       # URL重写规则
├── studio/                         # 管理后台目录
│   ├── index.php                   # 后台首页/仪表板
│   ├── edit.php                    # 文章编辑页面
│   ├── list.php                    # 文章列表页面
│   ├── settings.php                # 系统设置页面
│   ├── assets/                     # 后台专用资源
│   │   ├── css/
│   │   │   └── studio.css          # 后台样式
│   │   └── js/
│   │       ├── studio.js           # 后台JS功能
│   │       └── product-card.js     # 产品卡片组件
│   └── components/                 # 后台组件
│       ├── header.php              # 后台头部
│       ├── sidebar.php             # 后台侧边栏（可展开收缩）
│       ├── footer.php              # 后台底部
│       └── toast.php               # 后台Toast组件
├── assets/                         # 全局静态资源
│   ├── css/
│   │   ├── tailwind.css            # 本地Tailwind CSS
│   │   └── custom.css              # 自定义样式
│   ├── js/
│   │   ├── main.js                 # 主要JS功能
│   │   ├── json-parser.js          # JSON解析功能
│   │   ├── image-upload.js         # 图片上传功能
│   │   └── toast.js                # 前台Toast组件
│   └── images/                     # 静态图片资源
├── uploads/                        # 用户上传文件
│   ├── products/                   # 产品相关图片
│   ├── content/                    # 文章内容图片
│   └── thumbnails/                 # 缩略图
├── ckeditor/                       # CKEditor文件（已精简）
│   ├── ckeditor.js                 # 核心JS文件
│   ├── config.js                   # 配置文件
│   ├── contents.css                # 编辑器样式
│   ├── styles.js                   # 样式定义
│   ├── lang/                       # 语言包
│   │   ├── en.js                   # 英文（备用）
│   │   └── zh-cn.js                # 中文
│   ├── plugins/                    # 核心插件
│   │   ├── image/                  # 图片插件
│   │   └── ...                     # 其他必要插件
│   └── skins/                      # 皮肤
│       └── moono-lisa/             # 默认皮肤
├── includes/                       # PHP包含文件
│   ├── config.php                  # 数据库配置
│   ├── functions.php               # 通用函数
│   ├── database.php                # 数据库操作类
│   └── helpers.php                 # 辅助函数
├── api/                           # API接口
│   ├── upload-image.php           # 图片上传接口
│   ├── save-article.php           # 保存文章接口
│   ├── get-articles.php           # 获取文章列表接口
│   └── delete-article.php         # 删除文章接口
└── templates/                     # 前台模板
    ├── header.php                 # 前台头部
    ├── footer.php                 # 前台底部
    ├── toast.php                  # 前台Toast组件
    ├── article.php                # 文章详情模板
    └── home.php                   # 首页模板
```

访问路径：
- 前台：http://localhost/
- 后台：http://localhost/studio/
- 编辑页面：http://localhost/studio/edit.php


前台代码规范：
变量名、函数名、类名 - 英文
HTML文本内容 - 英文
CSS类名 - 英文
注释 - 中文（方便理解）
后台代码规范：
变量名、函数名、类名 - 英文
HTML文本内容 - 中文
CSS类名 - 英文
注释 - 中文

Toast 通知组件设计思路
功能特点：
统一调用 - 一个 JavaScript 函数搞定所有通知
多种类型 - 成功、错误、警告、信息
自动消失 - 可设置显示时长
可手动关闭 - 点击 X 按钮关闭
动画效果 - 滑入滑出动画
响应式 - 移动端友好
右上角弹出

